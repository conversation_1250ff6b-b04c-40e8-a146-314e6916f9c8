'use client';

import { useSession } from '@/lib/client.action';
import { isRestricted } from '@/lib/server.action';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

const StockTake = () => {
    const router = useRouter();
    const { scanning } = useSession();

    useEffect(() => {
        scanning();
    }, [scanning]);

    useEffect(() => {
        isRestricted().then(({ disabled }) => {
            if (disabled) {
                router.replace('/');
            }
        });
    }, [router]);

    return (
        <div className="grid">
            <div className="col-12">
                <div className="card">
                    <h5>Empty Page</h5>
                    <p>Use this page to start from scratch and place your custom content.</p>
                </div>
            </div>
        </div>
    );
};

export default StockTake;
