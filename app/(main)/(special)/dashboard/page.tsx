'use client';

import { useSession } from '@/lib/client.action';
import { getSignedSession } from '@/lib/server.action';
import { UserDocument } from '@/models/user.schema';
import { useEffect, useState } from 'react';

const greeting = () => {
    const currentHour = new Date().getHours();

    if (currentHour < 11) {
        return 'pagi';
    } else if (currentHour < 13) {
        return 'siang';
    } else if (currentHour < 18) {
        return 'sore';
    } else {
        return 'malam';
    }
};

const clocking = () => {
    const now = new Date();
    // Format time
    let hours = now.getHours();
    const minutes = now.getMinutes().toString().padStart(2, '0');
    const seconds = now.getSeconds().toString().padStart(2, '0');
    const ampm = hours >= 12 ? 'PM' : 'AM';
    hours = hours % 12;
    hours = hours || 12; // Convert 0 to 12
    // Format date
    const dateString = now.toLocaleDateString(undefined, { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' });

    return { date: dateString, time: `${hours}:${minutes}:${seconds} ${ampm}` };
};

const Dashboard = () => {
    const [signed, setSigned] = useState<UserDocument | undefined>();
    const [clock, setClock] = useState({ date: '', time: '' });
    const { scanning } = useSession();

    useEffect(() => {
        const attendance = async () => {
            const signed = await getSignedSession();

            if (signed) {
                setSigned(signed);
            }
        };

        attendance();
    }, []);

    useEffect(() => {
        scanning();
    }, [scanning]);

    useEffect(() => {
        const timer = setInterval(() => {
            setClock(clocking());
        }, 1000);

        return () => clearInterval(timer);
    }, []);

    return (
        <div className="grid">
            <div className="col-12">
                <div className="card">
                    <div className="surface-0">
                        <ul className="list-none p-0 m-0 flex align-items-center font-medium mb-3">
                            <li>
                                <a href="/" className="text-500 no-underline line-height-3 cursor-pointer">
                                    App
                                </a>
                            </li>
                            <li className="px-2">
                                <i className="pi pi-angle-right text-500 line-height-3"></i>
                            </li>
                            <li>
                                <span className="text-900 line-height-3">Beranda</span>
                            </li>
                        </ul>
                        <div className="flex align-items-start flex-column lg:justify-content-between lg:flex-row">
                            <div>
                                <div className="font-medium text-3xl text-900">
                                    Selamat {greeting()}, {signed?.name ?? signed?.privilege ?? 'Operator'}
                                </div>
                                <div className="flex align-items-center text-700 flex-wrap">
                                    <div className="mr-5 flex align-items-center mt-3">
                                        <i className="pi pi-calendar mr-2"></i>
                                        <span>{clock.date}</span>
                                    </div>
                                    <div className="mr-5 flex align-items-center mt-3">
                                        <i className="pi pi-clock mr-2"></i>
                                        <span>{clock.time}</span>
                                    </div>
                                    <div className="flex align-items-center mt-3">
                                        <i className="pi pi-key mr-2"></i>
                                        <span>{signed?.privilege}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Dashboard;
