'use server';

import handshakeDB from '@/lib/mongo';
import { createErrorResponse } from '@/lib/server.action';
import customerSchema from '@/models/customer.schema';
import productSchema from '@/models/product.schema';
import salesSchema, { SalesDocument } from '@/models/sales.schema';
import supplierSchema from '@/models/supplier.schema';
import dayjs from 'dayjs';
import { NextRequest } from 'next/server';

export async function GET(_: NextRequest) {
    let response: Response;

    try {
        await handshakeDB();
        const dateWhere: any = { $ne: null, $gte: new Date(dayjs().get('year'), 0, 1, 0, 0, 0, 0), $lt: new Date(dayjs().get('year') + 1, 0, 1, 0, 0, 0, 0) }; // get records for 1 year
        const customers = await customerSchema.countDocuments();
        const suppliers = await supplierSchema.countDocuments();
        const products = await productSchema.countDocuments();
        const items = await salesSchema
            .find({
                $or: [
                    { date: dateWhere },
                    {
                        $and: [{ $or: [{ date: null }, { date: { $exists: false } }] }, { 'author.created.time': dateWhere }]
                    }
                ]
            })
            .select('-__v')
            .sort({ date: 'desc', 'author.edited.time': 'desc', 'author.created.time': 'desc' })
            .populate({ path: 'products.product', select: '-__v -author' })
            .lean<SalesDocument[]>();
        const sales = items.map((item) => ({
            ...item,
            products: item.products.filter(({ product }) => product !== null)
        }));
        response = Response.json({ count: { customers, suppliers, products, sales: sales.length } }, { status: 200 });
    } catch (error) {
        response = createErrorResponse(error);
    }

    return response;
}
