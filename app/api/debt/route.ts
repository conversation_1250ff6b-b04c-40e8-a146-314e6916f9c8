'use server';

import handshakeDB from '@/lib/mongo';
import { createErrorResponse } from '@/lib/server.action';
import debitSchema, { DebitDocument } from '@/models/debit.schema';
import { create } from '@/mutations/debt/create';
import { getGlobalDateFilter } from '@/queries/get';
import dayjs from 'dayjs';
import { NextRequest } from 'next/server';

export async function GET(_: NextRequest) {
    let response: Response;

    try {
        await handshakeDB();
        const dateFilter = await getGlobalDateFilter('debt');
        let dateWhere: any = { $ne: null, $gte: new Date(dayjs().get('year'), 0, 1, 0, 0, 0, 0), $lt: new Date(dayjs().get('year') + 1, 0, 1, 0, 0, 0, 0) };

        if (dateFilter.length === 2) {
            if (dateFilter[0]) {
                if (!dateFilter[1]) {
                    dateWhere = { $gte: dateFilter[0], $lt: dayjs(dateFilter[0]).add(1, 'day').toDate() };
                } else {
                    dateWhere = { $gte: dateFilter[0], $lt: dayjs(dateFilter[1]).add(1, 'day').toDate() };
                }
            }
        }

        const debts = await debitSchema
            .find({
                $or: [
                    { date: dateWhere },
                    {
                        $and: [{ $or: [{ date: null }, { date: { $exists: false } }] }, { 'author.created.time': dateWhere }]
                    }
                ]
            })
            .select('-__v')
            .sort({ date: 'desc', 'author.edited.time': 'desc', 'author.created.time': 'desc' })
            .populate({ path: 'debt.supplier', select: '-__v' })
            .populate({ path: 'loan.customer', select: '-__v' })
            .populate({ path: 'author.created.by', select: '-__v' })
            .populate({ path: 'author.edited.by', select: '-__v' })
            .populate({ path: 'author.deleted.by', select: '-__v' })
            .lean<DebitDocument[]>();
        response = Response.json(debts, { status: 200 });
    } catch (error) {
        console.error(error);
        response = createErrorResponse(error);
    }

    return response;
}

export async function POST(request: NextRequest) {
    let response: Response;

    try {
        const params = await request.json();
        const saved = await create(params);
        response = Response.json({ saved: !!saved?._id }, { status: 200 });
    } catch (error) {
        response = createErrorResponse(error);
    }

    return response;
}
