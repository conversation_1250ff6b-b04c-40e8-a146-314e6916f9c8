'use server';

import { INVENTORY_CACHED } from '@/lib/const';
import redis from '@/lib/redis';

const baseUrl = process.env.NEXTAUTH_URL ?? process.env.NEXT_PUBLIC_BASE_URL ?? '';

const getData = async (section: string, param: string): Promise<any> => {
    try {
        const response = await fetch(`${baseUrl}/api/${section}/${param}`, {
            method: 'GET',
            headers: { 'Content-Type': 'application/json', 'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate', Pragma: 'no-cache', Expires: '0' },
            cache: 'no-store'
        });

        return await response.json();
    } catch (error) {
        console.error(error);

        return null;
    }
};

const getDataNoParam = async (section: string): Promise<any> => {
    try {
        const response = await fetch(`${baseUrl}/api/${section}`, {
            method: 'GET',
            headers: { 'Content-Type': 'application/json', 'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate', Pragma: 'no-cache', Expires: '0' },
            cache: 'no-store'
        });

        return await response.json();
    } catch (error) {
        console.error(error);

        return null;
    }
};

const getList = async (section: string): Promise<any[]> => {
    try {
        const response = await fetch(`${baseUrl}/api/${section}`, {
            method: 'GET',
            headers: { 'Content-Type': 'application/json', 'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate', Pragma: 'no-cache', Expires: '0' },
            cache: 'no-store'
        });

        return await response.json();
    } catch (error) {
        console.error(error);

        return [];
    }
};

const setInventoryDateFilter = async (key: 'sales' | 'receipt', date?: (Date | null)[] | null) => {
    try {
        await setGlobalDateFilter(key, date, 'inventory');
        await redis.del(INVENTORY_CACHED.RECEIPT, INVENTORY_CACHED.SALES);
    } catch (_) {
        console.error(_);
    }
};

const getInventoryDateFilter = async (key: 'sales' | 'receipt'): Promise<Date[]> => {
    return getGlobalDateFilter(key, 'inventory');
};

const setGlobalDateFilter = async (key: string, date?: (Date | null)[] | null, prefix?: string) => {
    try {
        await redis.set(`${prefix ?? 'global'}:date-filter:${key}`, JSON.stringify(date ?? []), 'EX', 3 * 60); // expired in 3 minutes
    } catch (_) {
        console.error(_);
    }
};

const getGlobalDateFilter = async (key: string, prefix?: string): Promise<Date[]> => {
    let filter = [];

    try {
        const result = await redis.get(`${prefix ?? 'global'}:date-filter:${key}`);
        filter = result ? JSON.parse(result) : [];
    } catch (_) {
        console.error(_);
    }

    return filter;
};

export { getData, getDataNoParam, getGlobalDateFilter, getInventoryDateFilter, getList, setGlobalDateFilter, setInventoryDateFilter };
