'use server';

import receiptSchema from '@/models/receipt.schema';
import salesSchema from '@/models/sales.schema';
import { UserDocument } from '@/models/user.schema';
import dayjs from 'dayjs';
import { existsSync, mkdirSync } from 'fs';
import { redirect } from 'next/navigation';
import { join } from 'path';
import { SESSION_STATE, STOCK_STATE } from './const';
import handshakeDB from './mongo';
import redis from './redis';

const getUploadedPath = (filename: string, dir = 'global') => {
    const uploadDir = join(process.cwd(), 'public', 'storage', 'image', dir);

    if (!existsSync(uploadDir)) {
        mkdirSync(uploadDir);
    }

    return join(uploadDir, filename);
};

const createErrorResponse = (error?: any): Response => Response.json({ error: error instanceof Error ? error.message : 'Server error', saved: false }, { status: 500 });

const createDefaultResponse = (error?: string): Response => Response.json({ error: error ?? 'Unprocessable operation!' }, { status: 422 });

const getSyncStockState = async () => {
    const locked = await redis.get(STOCK_STATE.SYNC);

    return { locked: !locked ? false : locked === 'true' };
};

const generateInvoiceNo = async (section: 'sales' | 'receipt') => {
    let count = 1;
    let number = String(count);

    try {
        await handshakeDB();

        if (section === 'sales') {
            count = await salesSchema.countDocuments({ date: { $gte: dayjs().startOf('day').toDate(), $lte: dayjs().endOf('day').toDate() } });
        } else {
            count = await receiptSchema.countDocuments({ date: { $gte: dayjs().startOf('day').toDate(), $lte: dayjs().endOf('day').toDate() } });
        }

        number = String(count + 1);
    } catch (_) {
        console.error(_);
    }

    return `${dayjs().format('YYMMDD.HHmmss')}.${number.length < 3 ? number.padStart(3, '0') : number}`;
};

const setSignedSession = async (user: UserDocument) => {
    let hasSigned = false;

    try {
        await redis.set(SESSION_STATE.KEY, JSON.stringify(user), 'EX', 86400); // expired in 1 day
        hasSigned = true;
    } catch (_) {
        console.error(_);
    }

    return hasSigned;
};

const getSignedSession = async () => {
    let session: UserDocument | null = null;

    try {
        const signed = await redis.get(SESSION_STATE.KEY);

        if (signed) {
            session = JSON.parse(signed);
        }
    } catch (_) {
        console.error(_);
    }

    return session;
};

const isRestricted = async () => {
    let restrict = true;

    try {
        const session = await redis.get(SESSION_STATE.KEY);

        if (session) {
            const signed: UserDocument = JSON.parse(session);
            restrict = signed?.privilege !== 'Super Admin';
        }
    } catch (_) {
        console.error(_);
    }

    return { disabled: restrict, visible: !restrict };
};

const goAway = async () => {
    let hasOut = false;

    try {
        const signedOut = await redis.del(SESSION_STATE.KEY);
        hasOut = signedOut >= 1;
    } catch (_) {
        console.error(_);
    }

    if (hasOut) {
        redirect('/');
    }
};

const printing = async (section: string, id: string) => {
    let printed = false;

    try {
        const response = await fetch(`${process.env.INTERNAL_SERVER}/print/${section}/${id}`, {
            method: 'GET',
            headers: { 'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate', Pragma: 'no-cache', Expires: '0' },
            cache: 'no-store'
        });
        const result = await response.json();
        printed = result?.printed ?? false;
    } catch (_) {
        console.error(_);
    }

    return printed;
};

export { createDefaultResponse, createErrorResponse, generateInvoiceNo, getSignedSession, getSyncStockState, getUploadedPath, goAway, isRestricted, printing, setSignedSession };
