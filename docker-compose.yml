version: '3.8'

services:
    mongodb:
        image: mongo:latest
        container_name: mongodb
        environment:
            MONGO_INITDB_ROOT_USERNAME: ${MONGO_ROOT_USERNAME}
            MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD}
            MONGO_INITDB_DATABASE: ${MONGO_DATABASE}
        volumes:
            - mongo_data:/data/db
        ports:
            - 27017:27017

    redis:
        image: redis:latest
        container_name: redis
        command: ['redis-server', '--requirepass', '${REDIS_PASSWORD}']
        volumes:
            - redis_data:/data
        ports:
            - 6379:6379

volumes:
    mongo_data:
        driver: local
        driver_opts:
            type: 'none'
            o: 'bind'
            device: '${VOLUME_PATH}/mongo'
    redis_data:
        driver: local
        driver_opts:
            type: 'none'
            o: 'bind'
            device: '${VOLUME_PATH}/redis'
