# System Config
MONGODB_URI=
REDIS_URL=
INTERNAL_SERVER=http://localhost:5000
AUTH_SECRET=

# Application Config
NEXT_PUBLIC_APP_NAME="Store Manager"
NEXT_PUBLIC_SHORT_NAME=SMTK
NEXT_PUBLIC_DESCRIPTION="Store Manager | Sistem Grosir dan <PERSON>"
NEXT_PUBLIC_BASE_URL=http://localhost:3000
NEXT_PUBLIC_INTERNAL_SERVER=http://localhost:5000

# Cloud Storage Config
CLOUD_STORAGE_APP_ACTIVE=false
CLOUD_STORAGE_APP_USER=
CLOUD_STORAGE_APP_CLIENT=
CLOUD_STORAGE_APP_SECRET=

# Docker Compose
MONGO_ROOT_USERNAME=
MONGO_ROOT_PASSWORD=
MONGO_DATABASE=store-manager-db
REDIS_PASSWORD=
VOLUME_PATH=~/Documents/dockerdev