# Use the official Node.js image as a base
FROM node:20.14.0

# Install necessary system dependencies for Chromium
# This ensures that <PERSON><PERSON><PERSON><PERSON> can launch a browser
RUN apt-get update && apt-get install -y \
    net-tools \
    iputils-ping \
    cups \
    cups-pdf \
    chromium \
    fonts-liberation \
    libappindicator3-1 \
    libasound2 \
    libatk-bridge2.0-0 \
    libatk1.0-0 \
    libcairo2 \
    libcups2 \
    libdbus-1-3 \
    libdrm2 \
    libgbm1 \
    libgconf-2-4 \
    libgdk-pixbuf2.0-0 \
    libglib2.0-0 \
    libgtk-3-0 \
    libnspr4 \
    libnss3 \
    libpango-1.0-0 \
    libpangocairo-1.0-0 \
    libx11-6 \
    libxcomposite1 \
    libxdamage1 \
    libxext6 \
    libxfixes3 \
    libxrandr2 \
    libxrender1 \
    libxss1 \
    libxtst6 \
    lsb-release \
    xdg-utils \
    --no-install-recommends && apt-get clean && rm -rf /var/lib/apt/lists/*

# Set environment variables
ENV PORT=3000

# Set Working Directory
WORKDIR /app

# Copy all files
COPY . .

# Copy environment file
COPY .env.deploy ./.env

# (re)Copy Package.json and lock files
COPY package.json package-lock.json ./

# Install dependencies
RUN npm install

# Expose application port
EXPOSE 3000
EXPOSE 631

# Start the Next.js application
CMD ["/bin/bash", "-c", "service cups start && npm start"]